package com.djcps.ai.aibackend.tools;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.djcps.ai.aibackend.tools.vo.TestTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;


@Service("text")
@RequiredArgsConstructor
@Slf4j
public class TextToolCallService implements ToolCallService{
    private final ChatClient chatClient;

    @Override
    public String prompt(TestTemplate runParam) {
        return StrUtil.format(AiConstant.date_template, DateUtil.today(), runParam.getCycleType());
    }


    @Override
    public Flux<String> stream(TestTemplate runParam) {
        String prompt = prompt(runParam);
        log.info("system prompt:<{}>,user prompt:<{}>",prompt,runParam.getBusinessPrompt());
        return chatClient.prompt(prompt)
                .user(runParam.getBusinessPrompt())
                .toolNames(runParam.getToolName())
                .stream()
                .content();
    }

    @Override
    public String call(TestTemplate runParam) {
        String prompt = prompt(runParam);
        log.info("system prompt:<{}>,user prompt:<{}>",prompt,runParam.getBusinessPrompt());
        return chatClient.prompt(prompt)
                .user(runParam.getBusinessPrompt())
                .toolNames(runParam.getToolName())
                .call()
                .content();
    }
}
