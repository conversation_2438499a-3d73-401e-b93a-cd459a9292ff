package com.djcps.ai.aibackend.tools;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.djcps.ai.aibackend.tools.vo.TestTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import static com.djcps.ai.aibackend.tools.AiConstant.date_template;


@Service("chart")
@RequiredArgsConstructor
@Slf4j
public class ChartToolCallService implements ToolCallService{
    private final ChatClient chatClient;
    @Override
    public String prompt(TestTemplate runParam) {
        String format = StrUtil.format(date_template, DateUtil.today(), runParam.getCycleType());
        format += ";请以JSON格式返回ECharts图表配置，包含完整的option对象，用于数据可视化展示。";
        return format;
    }

    @Override
    public Flux<String> stream(TestTemplate runParam) {
        String prompt = prompt(runParam);
        log.info("system prompt:<{}>,user prompt:<{}>",prompt,runParam.getBusinessPrompt());
        return chatClient.prompt(prompt(runParam))
                .user(runParam.getBusinessPrompt())
                .toolNames(runParam.getToolName())
                .stream()
                .content();
    }

    @Override
    public String call(TestTemplate runParam) {
        String prompt = prompt(runParam);
        log.info("system prompt:<{}>,user prompt:<{}>",prompt,runParam.getBusinessPrompt());
        return chatClient.prompt(prompt(runParam))
                .user(runParam.getBusinessPrompt())
                .toolNames(runParam.getToolName())
                .call()
                .content();
    }
}
