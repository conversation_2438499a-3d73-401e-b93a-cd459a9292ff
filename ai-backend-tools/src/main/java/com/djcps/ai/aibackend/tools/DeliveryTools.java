package com.djcps.ai.aibackend.tools;

import org.springframework.ai.tool.annotation.Tool;
import org.springframework.stereotype.Service;

@Service("tools_delivery")
public class DeliveryTools {


    @Tool(description = "获取交付率相关数据")
    public String delivery() {
        return """
                {
                    "undeliveredOrders": {
                        "orderCount": 128,
                        "customerCount": 43,
                        "areaInSqMeters": 2.87
                    },
                    "earliestUndeliveredOrderDate": "2025-06-10",
                    "deliveryRateThisMonth": {
                        "byOutbound": {
                            "rate": 96.2,
                            "monthOnMonthChange": 1.8
                        },
                        "byDelivery": {
                            "rate": 92.7,
                            "monthOnMonthChange": -0.5
                        }
                    },
                    "peakDeliveryPeriod": {
                        "timeRange": "14:00-16:00",
                        "90PercentDeliveryHours": 2
                    },
                    "keyRegionsToMonitor": ["朝阳区", "海淀区"],
                    "sensitiveCustomersCount": 15
                }
                
                """;
    }

}
