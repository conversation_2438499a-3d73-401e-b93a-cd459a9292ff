package com.djcps.ai.service;

import cn.hutool.core.util.StrUtil;
import com.djcps.ai.core.client.JobClient;
import com.djcps.ai.core.vo.CreateCronJob;
import com.djcps.ai.core.vo.JobVo;
import com.djcps.ai.core.vo.UpdateParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class JobService {
    private final JobClient jobClient;
    private static final String URL = "{}/task/run/{}";
    @Value("${current.host}")
    private String currentHOST;

    public String createCronTask(String cronExp, Long paramId) {
        String fullUrl = StrUtil.format(URL, currentHOST, paramId);
        log.info("fullUrl={}", fullUrl);
        CreateCronJob jobParam = new CreateCronJob(cronExp, fullUrl);

        JobVo jobVo = jobClient.createCronTask(jobParam);
        log.info("jobVo={}", jobVo);
        if (jobVo == null || 200 != jobVo.getCode()) {
            throw new RuntimeException("创建定时任务失败");
        }
        return jobVo.getMsg();
    }

    public boolean updateCronTask(Integer jobId, String cronExp) {
        UpdateParam jobParam = new UpdateParam(String.valueOf(jobId), cronExp);
        log.info("jobParam={}", jobParam);

        JobVo jobVo = jobClient.updateCronTask(jobParam);
        if (jobVo == null || 200 != jobVo.getCode()) {
            return false;
        }
        return true;
    }


    public boolean delJob(int jobId) {
        String res = jobClient.delJob(jobId);
        log.info("delJob jobVo={}", res);
        if (StrUtil.isBlankIfStr(res) || !StrUtil.contains(res, "200")) {
            return false;
        }
        return true;
    }


}
