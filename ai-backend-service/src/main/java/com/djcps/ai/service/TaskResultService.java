package com.djcps.ai.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.djcps.ai.dao.entity.TaskResult;
import com.djcps.ai.dao.mapper.TaskResultMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
@Slf4j
public class TaskResultService extends ServiceImpl<TaskResultMapper, TaskResult> {

    /**
     * 分页查询TaskResult
     * @param current 当前页
     * @param size 每页大小
     * @param skey 搜索关键字
     * @param cycleType 周期类型：day, week, month
     * @return 分页结果
     */
    public IPage<TaskResult> pageQuery(long current, long size, String skey, String cycleType) {
        Page<TaskResult> page = new Page<>(current, size);

        LambdaQueryWrapper<TaskResult> queryWrapper = new LambdaQueryWrapper<>();

        // 添加skey条件
        if (StringUtils.hasText(skey)) {
            queryWrapper.eq(TaskResult::getSkey, skey);
        }

        // 添加cycleType条件
        if (StringUtils.hasText(cycleType)) {
            queryWrapper.eq(TaskResult::getCycleType, cycleType);
        }

        // 按创建时间倒序排列
        queryWrapper.orderByDesc(TaskResult::getCreateTime);

        return page(page, queryWrapper);
    }
}
