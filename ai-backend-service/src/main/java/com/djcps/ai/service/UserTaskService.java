package com.djcps.ai.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.djcps.ai.aibackend.tools.ToolCallService;
import com.djcps.ai.aibackend.tools.vo.TestTemplate;
import com.djcps.ai.dao.entity.UserTask;
import com.djcps.ai.dao.entity.UserTemplate;
import com.djcps.ai.dao.mapper.UserTaskMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@Slf4j
@RequiredArgsConstructor
public class UserTaskService extends ServiceImpl<UserTaskMapper, UserTask> {
    private final Map<String, ToolCallService> toolCallServiceMap ;
    private final UserTemplateService userTemplateService;
    private final TaskResultService taskResultService;

    public void runTask(Long taskId) {
        UserTask task = getById(taskId);
        if (task == null) {
            log.info("当前xxlJob调用的id为:{}的 userTask未找到",taskId);
            return;
        }
        String paramList = task.getParamList();
        if (StrUtil.isBlankIfStr(paramList)) {
            log.info("当前xxlJob调用的id为:{}的 userTask参数列表为空",taskId);
            return;
        }
        Long templateId = task.getTemplateId();
        UserTemplate template = userTemplateService.getById(templateId);
        if (template == null) {
            log.info("当前xxlJob调用的id为:{}的 userTask对应的模板,id为{}未找到",taskId,templateId);
            return;
        }
        List<String> split = StrUtil.split(paramList, ",");
        TestTemplate testTemplate;
        for (String s : split) {
            log.info("参数: {}", s);
            testTemplate = new TestTemplate();
            testTemplate.setBusinessPrompt(template.getPrompt());
            testTemplate.setCycleType(task.getCycleType());
            testTemplate.setToolName(template.getApis());
            testTemplate.setResultType("text");
            ToolCallService toolCallService = toolCallServiceMap.get(testTemplate.getResultType());
            String call = toolCallService.call(testTemplate);
            System.out.println(call);

            taskResultService.save();
        }
    }
}
