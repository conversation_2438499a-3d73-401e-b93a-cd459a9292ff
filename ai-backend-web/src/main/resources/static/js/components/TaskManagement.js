// TaskManagement.js - 任务管理组件
const { useState, useEffect } = React;

function TaskManagement() {
    // 状态管理
    const [tasks, setTasks] = useState([]);
    const [templates, setTemplates] = useState([]);
    const [loading, setLoading] = useState(true);
    const [templatesLoading, setTemplatesLoading] = useState(false);
    const [selectedTask, setSelectedTask] = useState(null);
    const [isEditing, setIsEditing] = useState(false);
    const [formData, setFormData] = useState({
        title: '',
        remark: '',
        templateId: '',
        cornExp: '0 0 9 * * ?',  // 默认每天9点执行
        cycleType: '',
        paramList: ''
    });
    const [cronGenerating, setCronGenerating] = useState(false);
    const [naturalLanguageInput, setNaturalLanguageInput] = useState('');
    const [cronValidationError, setCronValidationError] = useState('');

    // 获取任务列表
    useEffect(() => {
        fetchTaskList();
        fetchTemplateList();
    }, []);

    // 从后端获取任务列表
    const fetchTaskList = async () => {
        try {
            setLoading(true);
            const response = await fetch('http://172.19.50.34:8080/ai-backend/task/list', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }

            const result = await response.json();

            // 检查响应格式
            if (result.success && result.data) {
                // 将后端数据格式转换为前端格式
                const mappedTasks = result.data.map(task => ({
                    id: task.id,
                    title: task.title || '',
                    remark: task.remark || '',
                    templateId: task.templateId || '',
                    cornExp: task.cornExp || '',
                    cycleType: task.cycleType || '',
                    paramList: task.paramList || '',
                    userId: task.userId || '',
                    createdAt: task.createTime ? new Date(task.createTime).toLocaleDateString() : '',
                    updatedAt: task.createTime ? new Date(task.createTime).toLocaleDateString() : ''
                }));

                setTasks(mappedTasks);
            } else {
                console.error('Invalid response format:', result);
                setTasks([]);
            }
        } catch (err) {
            console.error('Error fetching task list:', err);
            setTasks([]);
            window.showMessage.error('获取任务列表失败: ' + err.message);
        } finally {
            setLoading(false);
        }
    };

    // 从后端获取模板列表
    const fetchTemplateList = async () => {
        try {
            setTemplatesLoading(true);
            const response = await fetch('http://172.19.50.34:8080/ai-backend/template/list', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }

            const result = await response.json();

            if (result.success && result.data) {
                setTemplates(result.data);
            } else {
                console.error('Invalid template response format:', result);
                setTemplates([]);
            }
        } catch (err) {
            console.error('Error fetching template list:', err);
            setTemplates([]);
        } finally {
            setTemplatesLoading(false);
        }
    };

    // 选择任务
    const handleSelectTask = (task) => {
        setSelectedTask(task);
        setIsEditing(false);
        setFormData({
            title: task.title,
            remark: task.remark,
            templateId: task.templateId,
            cornExp: task.cornExp,
            cycleType: task.cycleType,
            paramList: task.paramList
        });
    };

    // 创建新任务
    const handleCreateNew = () => {
        setFormData({
            title: '',
            remark: '',
            templateId: '',
            cornExp: '0 0 9 * * ?',
            cycleType: '',
            paramList: ''
        });
        setSelectedTask(null);
        setIsEditing(true);
    };

    // 编辑任务
    const handleEdit = () => {
        if (!selectedTask) return;
        
        setFormData({
            title: selectedTask.title,
            remark: selectedTask.remark,
            templateId: selectedTask.templateId,
            cornExp: selectedTask.cornExp,
            cycleType: selectedTask.cycleType,
            paramList: selectedTask.paramList
        });
        setIsEditing(true);
    };

    // 取消编辑
    const handleCancel = () => {
        setIsEditing(false);
        if (selectedTask) {
            // 如果是编辑现有任务，恢复选中状态
            setSelectedTask(selectedTask);
        }
    };

    // 处理表单输入变化
    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));

        // 如果是Cron表达式字段，进行实时校验
        if (name === 'cornExp') {
            const validationError = validateCronExpression(value);
            setCronValidationError(validationError || '');
        }
    };

    // 保存任务
    const handleSave = async () => {
        // 验证表单
        if (!formData.title.trim()) {
            window.showMessage.warning('任务标题不能为空');
            return;
        }

        if (!formData.templateId) {
            window.showMessage.warning('请选择关联模板');
            return;
        }

        if (!formData.cornExp.trim()) {
            window.showMessage.warning('Cron表达式不能为空');
            return;
        }

        // 校验Cron表达式
        const cronValidationError = validateCronExpression(formData.cornExp);
        if (cronValidationError) {
            window.showMessage.warning(`Cron表达式格式错误: ${cronValidationError}`);
            setCronValidationError(cronValidationError);
            return;
        }

        try {
            // 构建要保存的任务数据
            const taskData = {
                id: selectedTask ? selectedTask.id : null,
                title: formData.title,
                remark: formData.remark,
                templateId: parseInt(formData.templateId),
                cornExp: formData.cornExp,
                cycleType: formData.cycleType,
                paramList: formData.paramList
            };

            const response = await fetch('http://172.19.50.34:8080/ai-backend/task/save', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(taskData)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }

            const result = await response.json();

            if (result.success && result.data) {
                // 将后端返回的数据转换为前端格式
                const savedTask = {
                    id: result.data.id,
                    title: result.data.title || '',
                    remark: result.data.remark || '',
                    templateId: result.data.templateId || '',
                    cornExp: result.data.cornExp || '',
                    cycleType: result.data.cycleType || '',
                    paramList: result.data.paramList || '',
                    userId: result.data.userId || '',
                    createdAt: result.data.createTime ? new Date(result.data.createTime).toLocaleDateString() : '',
                    updatedAt: result.data.createTime ? new Date(result.data.createTime).toLocaleDateString() : ''
                };

                // 重新加载任务列表以确保数据同步
                await fetchTaskList();

                // 设置选中的任务
                setSelectedTask(savedTask);
                setIsEditing(false);
                window.showMessage.success('任务保存成功！');
            } else {
                throw new Error(result.msg || '保存失败');
            }
        } catch (error) {
            console.error('保存任务失败:', error);
            window.showMessage.error('保存任务失败: ' + error.message);
        }
    };

    // 删除任务
    const handleDelete = async () => {
        if (!selectedTask) return;

        const confirmed = await window.showConfirm(`确定要删除任务 "${selectedTask.title}" 吗？`);
        if (confirmed) {
            try {
                const response = await fetch(`http://172.19.50.34:8080/ai-backend/task/delete/${selectedTask.id}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }

                const result = await response.json();

                if (result.success) {
                    // 重新加载任务列表以确保数据同步
                    await fetchTaskList();
                    setSelectedTask(null);
                    window.showMessage.success('任务删除成功！');
                } else {
                    throw new Error(result.msg || '删除失败');
                }
            } catch (error) {
                console.error('删除任务失败:', error);
                window.showMessage.error('删除任务失败: ' + error.message);
            }
        }
    };

    // 获取模板名称
    const getTemplateName = (templateId) => {
        const template = templates.find(t => t.id === templateId);
        return template ? template.title : '未知模板';
    };

    // 常用Cron表达式预设
    const cronPresets = [
        { label: '每天9点', value: '0 0 9 * * ?' },
        { label: '每天18点', value: '0 0 18 * * ?' },
        { label: '每周一9点', value: '0 0 9 ? * MON' },
        { label: '每月1号9点', value: '0 0 9 1 * ?' },
        { label: '每小时', value: '0 0 * * * ?' },
        { label: '每30分钟', value: '0 */30 * * * ?' }
    ];

    // Cron表达式校验函数
    const validateCronExpression = (cronExp) => {
        if (!cronExp || !cronExp.trim()) {
            return 'Cron表达式不能为空';
        }

        const parts = cronExp.trim().split(/\s+/);
        if (parts.length !== 6) {
            return 'Cron表达式必须包含6个字段：秒 分 时 日 月 周';
        }

        // 基本格式校验
        const [second, minute, hour, day, month, week] = parts;

        // 秒校验 (0-59)
        if (!isValidCronField(second, 0, 59)) {
            return '秒字段无效，应为0-59或*或*/n格式';
        }

        // 分钟校验 (0-59)
        if (!isValidCronField(minute, 0, 59)) {
            return '分钟字段无效，应为0-59或*或*/n格式';
        }

        // 小时校验 (0-23)
        if (!isValidCronField(hour, 0, 23)) {
            return '小时字段无效，应为0-23或*或*/n格式';
        }

        // 日期校验 (1-31 或 ?)
        if (day !== '?' && !isValidCronField(day, 1, 31)) {
            return '日期字段无效，应为1-31或?或*或*/n格式';
        }

        // 月份校验 (1-12)
        if (!isValidCronField(month, 1, 12)) {
            return '月份字段无效，应为1-12或*或*/n格式';
        }

        // 周校验 (1-7 或 MON-SUN 或 ?)
        if (week !== '?' && !isValidWeekField(week)) {
            return '周字段无效，应为1-7或MON-SUN或?或*格式';
        }

        // 日期和周不能同时指定
        if (day !== '?' && week !== '?') {
            return '日期和周字段不能同时指定，其中一个必须为?';
        }

        return null; // 校验通过
    };

    // 校验Cron字段的辅助函数
    const isValidCronField = (field, min, max) => {
        if (field === '*') return true;
        if (field === '?') return true;

        // 处理 */n 格式
        if (field.includes('*/')) {
            const step = parseInt(field.split('*/')[1]);
            return !isNaN(step) && step > 0 && step <= max;
        }

        // 处理范围 n-m 格式
        if (field.includes('-')) {
            const [start, end] = field.split('-').map(n => parseInt(n));
            return !isNaN(start) && !isNaN(end) && start >= min && end <= max && start <= end;
        }

        // 处理列表 n,m,k 格式
        if (field.includes(',')) {
            const values = field.split(',').map(n => parseInt(n));
            return values.every(v => !isNaN(v) && v >= min && v <= max);
        }

        // 处理单个数字
        const num = parseInt(field);
        return !isNaN(num) && num >= min && num <= max;
    };

    // 校验周字段的辅助函数
    const isValidWeekField = (field) => {
        if (field === '*' || field === '?') return true;

        const weekNames = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];

        // 处理周名称
        if (weekNames.includes(field.toUpperCase())) return true;

        // 处理数字 (1-7)
        const num = parseInt(field);
        if (!isNaN(num) && num >= 1 && num <= 7) return true;

        // 处理范围和列表
        if (field.includes('-') || field.includes(',')) {
            return field.split(/[-,]/).every(part => {
                const trimmed = part.trim();
                return weekNames.includes(trimmed.toUpperCase()) ||
                       (!isNaN(parseInt(trimmed)) && parseInt(trimmed) >= 1 && parseInt(trimmed) <= 7);
            });
        }

        return false;
    };

    // 智能生成Cron表达式
    const generateCronExpression = async () => {
        if (!naturalLanguageInput.trim()) {
            window.showMessage.warning('请输入自然语言描述');
            return;
        }

        try {
            setCronGenerating(true);
            console.log('开始生成Cron表达式，输入:', naturalLanguageInput);

            const requestBody = {
                key: 'prompt_gene_corn',
                content: naturalLanguageInput
            };
            console.log('请求体:', requestBody);

            const response = await fetch('http://172.19.50.34:8080/ai-backend/generate/corn', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            console.log('响应状态:', response.status);
            console.log('响应头:', Object.fromEntries(response.headers.entries()));

            if (!response.ok) {
                const errorText = await response.text();
                console.error('HTTP错误响应:', errorText);
                throw new Error(`HTTP error! Status: ${response.status}, Message: ${errorText}`);
            }

            // 处理流式响应
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let generatedCron = '';
            let chunkCount = 0;

            console.log('开始读取流式响应...');

            while (true) {
                const { done, value } = await reader.read();
                if (done) {
                    console.log('流式响应读取完成，总共接收到', chunkCount, '个数据块');
                    break;
                }

                const chunk = decoder.decode(value, { stream: true });
                chunkCount++;
                console.log(`接收到第${chunkCount}个数据块:`, JSON.stringify(chunk));
                generatedCron += chunk;
            }

            console.log('完整的生成内容:', JSON.stringify(generatedCron));
            console.log('生成内容长度:', generatedCron.length);

            // 清理生成的Cron表达式
            let cleanedCron = generatedCron.trim();
            console.log('清理后的内容:', JSON.stringify(cleanedCron));

            // 尝试多种正则表达式来提取Cron表达式
            const cronPatterns = [
                // 标准6字段Cron表达式
                /(\d+\s+\d+\s+\d+\s+[*?\d]+\s+[*\d]+\s+[*?\w]+)/,
                // 更宽松的匹配
                /([0-9*?\/,-]+\s+[0-9*?\/,-]+\s+[0-9*?\/,-]+\s+[0-9*?\/,-]+\s+[0-9*?\/,-]+\s+[0-9*?\/,A-Z-]+)/,
                // 匹配包含问号的表达式
                /([0-9*?\/,-]+\s+[0-9*?\/,-]+\s+[0-9*?\/,-]+\s+[0-9*?\/,-?]+\s+[0-9*?\/,-]+\s+[0-9*?\/,A-Z-?]+)/
            ];

            let cronMatch = null;
            for (let i = 0; i < cronPatterns.length; i++) {
                cronMatch = cleanedCron.match(cronPatterns[i]);
                if (cronMatch) {
                    console.log(`使用第${i+1}个正则表达式匹配成功:`, cronMatch[1]);
                    cleanedCron = cronMatch[1];
                    break;
                }
            }

            if (!cronMatch) {
                console.log('未找到标准Cron表达式，尝试直接使用生成的内容');
                // 如果没有匹配到，尝试按行分割并找到看起来像Cron表达式的行
                const lines = cleanedCron.split('\n').map(line => line.trim()).filter(line => line);
                console.log('分割后的行:', lines);

                for (const line of lines) {
                    const parts = line.split(/\s+/);
                    if (parts.length === 6) {
                        console.log('找到6字段的行:', line);
                        cleanedCron = line;
                        break;
                    }
                }
            }

            console.log('最终的Cron表达式:', JSON.stringify(cleanedCron));

            // 校验生成的Cron表达式
            const validationError = validateCronExpression(cleanedCron);
            console.log('校验结果:', validationError || '校验通过');

            if (validationError) {
                console.error('生成的Cron表达式校验失败:', validationError);
                console.error('原始生成内容:', JSON.stringify(generatedCron));
                window.showMessage.warning(`生成的Cron表达式格式有误: ${validationError}\n生成的内容: ${cleanedCron}`);
                return;
            }

            // 更新表单数据
            setFormData(prev => ({ ...prev, cornExp: cleanedCron }));
            setCronValidationError('');
            setNaturalLanguageInput('');
            window.showMessage.success('Cron表达式生成成功！');

        } catch (error) {
            console.error('生成Cron表达式失败:', error);
            window.showMessage.error('生成Cron表达式失败: ' + error.message);
        } finally {
            setCronGenerating(false);
        }
    };

    // 渲染任务详情
    const renderTaskDetail = () => {
        if (!selectedTask) {
            return (
                <div className="flex flex-col items-center justify-center h-full text-gray-500">
                    <svg className="w-16 h-16 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                    </svg>
                    <p>选择一个任务查看详情</p>
                </div>
            );
        }

        return (
            <div className="h-full flex flex-col">
                <div className="flex justify-between items-center mb-4">
                    <h3 className="text-xl font-semibold">{selectedTask.title}</h3>
                    <div className="flex space-x-2">
                        <button
                            onClick={handleEdit}
                            className="px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 text-sm"
                        >
                            编辑
                        </button>
                        <button
                            onClick={handleDelete}
                            className="px-3 py-1 bg-red-500 text-white rounded-md hover:bg-red-600 text-sm"
                        >
                            删除
                        </button>
                    </div>
                </div>

                <div className="space-y-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">任务描述:</label>
                        <p className="text-gray-600">{selectedTask.remark || '无描述'}</p>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">关联模板:</label>
                        <p className="text-gray-600">{getTemplateName(selectedTask.templateId)}</p>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Cron表达式:</label>
                        <p className="text-gray-600 font-mono">{selectedTask.cornExp}</p>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">周期类型:</label>
                        <p className="text-gray-600">
                            {selectedTask.cycleType === 'day' ? '日' :
                             selectedTask.cycleType === 'week' ? '周' :
                             selectedTask.cycleType === 'month' ? '月' :
                             selectedTask.cycleType || '未设置'}
                        </p>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">参数列表:</label>
                        <p className="text-gray-600">{selectedTask.paramList || '无参数'}</p>
                    </div>

                    <div className="text-sm text-gray-500">
                        <p>创建于: {selectedTask.createdAt}</p>
                        <p>更新于: {selectedTask.updatedAt}</p>
                    </div>
                </div>
            </div>
        );
    };

    // 渲染创建/编辑表单
    const renderTaskForm = () => (
        <div className="h-full flex flex-col">
            <h3 className="text-xl font-semibold mb-4">{selectedTask ? '编辑任务' : '创建新任务'}</h3>
            <div className="space-y-4 flex-1">
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">任务标题 *</label>
                    <input
                        type="text"
                        name="title"
                        value={formData.title}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="输入任务标题"
                    />
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">任务描述</label>
                    <textarea
                        name="remark"
                        value={formData.remark}
                        onChange={handleInputChange}
                        rows="3"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="输入任务描述"
                    />
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">关联模板 *</label>
                    <select
                        name="templateId"
                        value={formData.templateId}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        disabled={templatesLoading}
                    >
                        <option value="">选择关联模板</option>
                        {templates.map(template => (
                            <option key={template.id} value={template.id}>
                                {template.title} - {template.remark}
                            </option>
                        ))}
                    </select>
                    {templatesLoading && (
                        <p className="text-sm text-gray-500 mt-1">加载模板列表中...</p>
                    )}
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">周期类型</label>
                    <select
                        name="cycleType"
                        value={formData.cycleType}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        <option value="">选择周期类型</option>
                        <option value="day">日</option>
                        <option value="week">周</option>
                        <option value="month">月</option>
                    </select>
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">参数列表</label>
                    <input
                        type="text"
                        name="paramList"
                        value={formData.paramList}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="输入参数，多个参数用英文逗号分割"
                    />
                    <p className="text-xs text-gray-500 mt-1">例：param1,param2,param3</p>
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Cron表达式 *</label>
                    <div className="space-y-3">
                        {/* 智能生成区域 */}
                        <div className="bg-blue-50 p-3 rounded-md border border-blue-200">
                            <label className="block text-sm font-medium text-blue-700 mb-2">智能生成</label>
                            <div className="flex gap-2">
                                <input
                                    type="text"
                                    value={naturalLanguageInput}
                                    onChange={(e) => setNaturalLanguageInput(e.target.value)}
                                    className="flex-1 px-3 py-2 border border-blue-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                                    placeholder="例：每天上午9点执行、每周一下午3点执行、每月1号执行"
                                    disabled={cronGenerating}
                                />
                                <button
                                    type="button"
                                    onClick={generateCronExpression}
                                    disabled={cronGenerating || !naturalLanguageInput.trim()}
                                    className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-sm flex items-center"
                                >
                                    {cronGenerating ? (
                                        <>
                                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                            生成中...
                                        </>
                                    ) : (
                                        '智能生成'
                                    )}
                                </button>
                            </div>
                        </div>

                        {/* Cron表达式输入 */}
                        <div>
                            <input
                                type="text"
                                name="cornExp"
                                value={formData.cornExp}
                                onChange={handleInputChange}
                                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 font-mono ${
                                    cronValidationError
                                        ? 'border-red-300 focus:ring-red-500'
                                        : 'border-gray-300 focus:ring-blue-500'
                                }`}
                                placeholder="输入Cron表达式"
                            />
                            {cronValidationError && (
                                <p className="text-red-500 text-xs mt-1">{cronValidationError}</p>
                            )}
                        </div>

                        {/* 预设按钮 */}
                        <div className="flex flex-wrap gap-2">
                            {cronPresets.map((preset, index) => (
                                <button
                                    key={index}
                                    type="button"
                                    onClick={() => {
                                        setFormData(prev => ({ ...prev, cornExp: preset.value }));
                                        setCronValidationError('');
                                    }}
                                    className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
                                >
                                    {preset.label}
                                </button>
                            ))}
                        </div>

                        <p className="text-xs text-gray-500">
                            Cron表达式格式: 秒 分 时 日 月 周 (例: 0 0 9 * * ? 表示每天9点执行)
                        </p>
                    </div>
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                    <button
                        onClick={handleCancel}
                        className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                    >
                        取消
                    </button>
                    <button
                        onClick={handleSave}
                        className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                    >
                        保存
                    </button>
                </div>
            </div>
        </div>
    );

    // 主渲染函数
    return (
        <div className="h-full flex flex-col text-gray-800">
            <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold">任务管理</h2>
                {!isEditing && (
                    <button
                        onClick={handleCreateNew}
                        className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                    >
                        创建新任务
                    </button>
                )}
            </div>

            {loading ? (
                <div className="flex justify-center items-center h-64">
                    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                </div>
            ) : (
                <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-6 h-full">
                    {/* 任务列表 */}
                    <div className="bg-white bg-opacity-80 rounded-xl shadow-sm overflow-hidden">
                        <div className="p-4 border-b border-gray-200">
                            <h3 className="font-medium">任务列表</h3>
                        </div>
                        <div className="overflow-y-auto" style={{ maxHeight: 'calc(100vh - 280px)' }}>
                            {tasks.length === 0 ? (
                                <div className="p-4 text-center text-gray-500">
                                    暂无任务，点击"创建新任务"按钮添加
                                </div>
                            ) : (
                                <ul className="divide-y divide-gray-200">
                                    {tasks.map(task => (
                                        <li
                                            key={task.id}
                                            className={`p-4 cursor-pointer hover:bg-gray-50 transition-colors ${selectedTask && selectedTask.id === task.id ? 'bg-blue-50' : ''}`}
                                            onClick={() => handleSelectTask(task)}
                                        >
                                            <h4 className="font-medium">{task.title}</h4>
                                            <p className="text-sm text-gray-500 mt-1 truncate">{task.remark || '无描述'}</p>
                                            <div className="text-xs text-gray-400 mt-2">
                                                <p>模板: {getTemplateName(task.templateId)}</p>
                                                <p>更新于: {task.updatedAt}</p>
                                            </div>
                                        </li>
                                    ))}
                                </ul>
                            )}
                        </div>
                    </div>

                    {/* 任务详情及编辑区 */}
                    <div className="bg-white bg-opacity-80 rounded-xl shadow-sm overflow-hidden md:col-span-2 p-6">
                        {isEditing ? renderTaskForm() : renderTaskDetail()}
                    </div>
                </div>
            )}
        </div>
    );
}
