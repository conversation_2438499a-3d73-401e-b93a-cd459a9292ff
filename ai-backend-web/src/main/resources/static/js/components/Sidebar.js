// Sidebar.js - 左侧导航栏组件
function Sidebar({ activeNav, setActiveNav }) {
    // 导航项配置
    const navItems = [
        { id: 'api', name: '接口管理', icon: '🔌' },
        { id: 'template', name: '模板管理', icon: '📄' },
        { id: 'task', name: '任务管理', icon: '⏰' },
        { id: 'taskResult', name: '任务结果', icon: '📊' },
    ];
    
    return (
        <aside className="w-64 h-full glass rounded-r-xl">
            <div className="p-6">
                <h1 className="text-2xl font-bold text-white mb-8">管理系统</h1>
                
                <nav className="space-y-2">
                    {navItems.map(item => (
                        <button
                            key={item.id}
                            className={`flex items-center w-full p-3 rounded-lg transition-all duration-200 ${activeNav === item.id ? 'bg-white bg-opacity-30 text-white' : 'text-gray-200 hover:bg-white hover:bg-opacity-10'}`}
                            onClick={() => setActiveNav(item.id)}
                        >
                            <span className="text-xl mr-3">{item.icon}</span>
                            <span className="font-medium">{item.name}</span>
                        </button>
                    ))}
                </nav>
            </div>
        </aside>
    );
}