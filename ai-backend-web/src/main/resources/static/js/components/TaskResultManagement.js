// TaskResultManagement.js - 任务结果管理组件
const { useState, useEffect } = React;

function TaskResultManagement() {
    // 状态管理
    const [taskResults, setTaskResults] = useState([]);
    const [loading, setLoading] = useState(true);
    const [pagination, setPagination] = useState({
        current: 1,
        size: 10,
        total: 0
    });
    const [queryParams, setQueryParams] = useState({
        skey: '',
        cycleType: ''
    });
    const [selectedResult, setSelectedResult] = useState(null);
    const [showDetailModal, setShowDetailModal] = useState(false);

    // 周期类型选项
    const cycleTypeOptions = [
        { value: '', label: '全部' },
        { value: 'day', label: '日' },
        { value: 'week', label: '周' },
        { value: 'month', label: '月' }
    ];

    // 获取任务结果列表
    useEffect(() => {
        fetchTaskResults();
    }, [pagination.current, pagination.size]);

    // 从后端获取任务结果列表
    const fetchTaskResults = async () => {
        try {
            setLoading(true);
            const requestBody = {
                current: pagination.current,
                size: pagination.size,
                skey: queryParams.skey || undefined,
                cycleType: queryParams.cycleType || undefined
            };

            const response = await fetch('http://172.19.50.34:8080/ai-backend/taskResult/page', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }

            const result = await response.json();

            if (result.success && result.data) {
                // 根据实际的PageResult结构调整
                setTaskResults(result.data.list || []);
                setPagination(prev => ({
                    ...prev,
                    total: result.data.total || 0
                }));
            } else {
                console.error('Invalid response format:', result);
                setTaskResults([]);
                window.showMessage.error(result.msg || '获取任务结果列表失败');
            }
        } catch (err) {
            console.error('Error fetching task results:', err);
            setTaskResults([]);
            window.showMessage.error('获取任务结果列表失败: ' + err.message);
        } finally {
            setLoading(false);
        }
    };

    // 处理搜索
    const handleSearch = () => {
        setPagination(prev => ({ ...prev, current: 1 }));
        fetchTaskResults();
    };

    // 处理重置
    const handleReset = () => {
        setQueryParams({
            skey: '',
            cycleType: ''
        });
        setPagination(prev => ({ ...prev, current: 1 }));
        // 重置后需要重新查询
        setTimeout(() => {
            fetchTaskResults();
        }, 0);
    };

    // 处理分页变化
    const handlePageChange = (newPage) => {
        setPagination(prev => ({ ...prev, current: newPage }));
    };

    // 处理每页大小变化
    const handlePageSizeChange = (newSize) => {
        setPagination(prev => ({ ...prev, size: newSize, current: 1 }));
    };

    // 格式化日期
    const formatDate = (dateString) => {
        if (!dateString) return '-';
        return new Date(dateString).toLocaleString('zh-CN');
    };

    // 格式化周期类型
    const formatCycleType = (cycleType) => {
        const option = cycleTypeOptions.find(opt => opt.value === cycleType);
        return option ? option.label : cycleType || '-';
    };

    // 查看详情
    const handleViewDetail = (result) => {
        setSelectedResult(result);
        setShowDetailModal(true);
    };

    // 关闭详情弹窗
    const handleCloseDetail = () => {
        setShowDetailModal(false);
        setSelectedResult(null);
    };

    // 渲染分页组件
    const renderPagination = () => {
        if (pagination.total === 0) return null;

        const totalPages = Math.ceil(pagination.total / pagination.size);
        const startPage = Math.max(1, pagination.current - 2);
        const endPage = Math.min(totalPages, pagination.current + 2);
        const pages = [];

        for (let i = startPage; i <= endPage; i++) {
            pages.push(i);
        }

        return (
            <div className="flex items-center justify-between mt-6">
                <div className="text-sm text-gray-700">
                    共 {pagination.total} 条记录，第 {pagination.current} / {totalPages} 页
                </div>
                <div className="flex items-center space-x-2">
                    <button
                        onClick={() => handlePageChange(1)}
                        disabled={pagination.current === 1}
                        className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:bg-gray-100 disabled:cursor-not-allowed hover:bg-gray-50"
                    >
                        首页
                    </button>
                    <button
                        onClick={() => handlePageChange(pagination.current - 1)}
                        disabled={pagination.current === 1}
                        className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:bg-gray-100 disabled:cursor-not-allowed hover:bg-gray-50"
                    >
                        上一页
                    </button>

                    {pages.map(page => (
                        <button
                            key={page}
                            onClick={() => handlePageChange(page)}
                            className={`px-3 py-1 border rounded-md text-sm ${
                                page === pagination.current
                                    ? 'bg-blue-500 text-white border-blue-500'
                                    : 'border-gray-300 hover:bg-gray-50'
                            }`}
                        >
                            {page}
                        </button>
                    ))}

                    <button
                        onClick={() => handlePageChange(pagination.current + 1)}
                        disabled={pagination.current === totalPages}
                        className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:bg-gray-100 disabled:cursor-not-allowed hover:bg-gray-50"
                    >
                        下一页
                    </button>
                    <button
                        onClick={() => handlePageChange(totalPages)}
                        disabled={pagination.current === totalPages}
                        className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:bg-gray-100 disabled:cursor-not-allowed hover:bg-gray-50"
                    >
                        末页
                    </button>

                    <select
                        value={pagination.size}
                        onChange={(e) => handlePageSizeChange(parseInt(e.target.value))}
                        className="px-2 py-1 border border-gray-300 rounded-md text-sm"
                    >
                        <option value={10}>10条/页</option>
                        <option value={20}>20条/页</option>
                        <option value={50}>50条/页</option>
                        <option value={100}>100条/页</option>
                    </select>
                </div>
            </div>
        );
    };

    // 主渲染函数
    return (
        <div className="h-full flex flex-col text-gray-800">
            <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold">任务结果管理</h2>
            </div>

            {/* 搜索条件 */}
            <div className="bg-white bg-opacity-80 rounded-xl shadow-sm p-4 mb-6">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">搜索关键字</label>
                        <input
                            type="text"
                            value={queryParams.skey}
                            onChange={(e) => setQueryParams(prev => ({ ...prev, skey: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="输入skey搜索"
                        />
                    </div>
                    
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">周期类型</label>
                        <select
                            value={queryParams.cycleType}
                            onChange={(e) => setQueryParams(prev => ({ ...prev, cycleType: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            {cycleTypeOptions.map(option => (
                                <option key={option.value} value={option.value}>
                                    {option.label}
                                </option>
                            ))}
                        </select>
                    </div>
                    
                    <div className="flex items-end space-x-2">
                        <button
                            onClick={handleSearch}
                            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                        >
                            搜索
                        </button>
                        <button
                            onClick={handleReset}
                            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                        >
                            重置
                        </button>
                    </div>
                </div>
            </div>

            {/* 数据表格 */}
            <div className="bg-white bg-opacity-80 rounded-xl shadow-sm flex-1 overflow-hidden">
                {loading ? (
                    <div className="flex justify-center items-center h-64">
                        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                    </div>
                ) : (
                    <div className="h-full flex flex-col">
                        <div className="overflow-x-auto flex-1">
                            <table className="w-full">
                                <thead className="bg-gray-50 border-b border-gray-200">
                                    <tr>
                                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Skey</th>
                                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">任务ID</th>
                                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">周期类型</th>
                                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">提示词</th>
                                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">执行开始时间</th>
                                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">执行结束时间</th>
                                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {taskResults.length === 0 ? (
                                        <tr>
                                            <td colSpan="9" className="px-4 py-8 text-center text-gray-500">
                                                暂无数据
                                            </td>
                                        </tr>
                                    ) : (
                                        taskResults.map(result => (
                                            <tr key={result.id} className="hover:bg-gray-50">
                                                <td className="px-4 py-3 text-sm text-gray-900">{result.id}</td>
                                                <td className="px-4 py-3 text-sm text-gray-900">{result.skey || '-'}</td>
                                                <td className="px-4 py-3 text-sm text-gray-900">{result.taskId || '-'}</td>
                                                <td className="px-4 py-3 text-sm text-gray-900">
                                                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                                        result.cycleType === 'day' ? 'bg-green-100 text-green-800' :
                                                        result.cycleType === 'week' ? 'bg-blue-100 text-blue-800' :
                                                        result.cycleType === 'month' ? 'bg-purple-100 text-purple-800' :
                                                        'bg-gray-100 text-gray-800'
                                                    }`}>
                                                        {formatCycleType(result.cycleType)}
                                                    </span>
                                                </td>
                                                <td className="px-4 py-3 text-sm text-gray-900 max-w-xs truncate" title={result.prompt}>
                                                    {result.prompt || '-'}
                                                </td>
                                                <td className="px-4 py-3 text-sm text-gray-900">{formatDate(result.execStartTime)}</td>
                                                <td className="px-4 py-3 text-sm text-gray-900">{formatDate(result.execEndTime)}</td>
                                                <td className="px-4 py-3 text-sm text-gray-900">{formatDate(result.createTime)}</td>
                                                <td className="px-4 py-3 text-sm text-gray-900">
                                                    <button
                                                        onClick={() => handleViewDetail(result)}
                                                        className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                                                    >
                                                        查看详情
                                                    </button>
                                                </td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </table>
                        </div>
                        
                        {/* 分页组件 */}
                        <div className="px-4 py-3 border-t border-gray-200">
                            {renderPagination()}
                        </div>
                    </div>
                )}
            </div>

            {/* 详情弹窗 */}
            {showDetailModal && selectedResult && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
                        <div className="flex justify-between items-center p-6 border-b border-gray-200">
                            <h3 className="text-lg font-semibold text-gray-900">任务结果详情</h3>
                            <button
                                onClick={handleCloseDetail}
                                className="text-gray-400 hover:text-gray-600"
                            >
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">ID</label>
                                    <p className="text-gray-900">{selectedResult.id}</p>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Skey</label>
                                    <p className="text-gray-900">{selectedResult.skey || '-'}</p>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">任务ID</label>
                                    <p className="text-gray-900">{selectedResult.taskId || '-'}</p>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">周期类型</label>
                                    <p className="text-gray-900">{formatCycleType(selectedResult.cycleType)}</p>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">执行开始时间</label>
                                    <p className="text-gray-900">{formatDate(selectedResult.execStartTime)}</p>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">执行结束时间</label>
                                    <p className="text-gray-900">{formatDate(selectedResult.execEndTime)}</p>
                                </div>
                                <div className="md:col-span-2">
                                    <label className="block text-sm font-medium text-gray-700 mb-1">提示词</label>
                                    <div className="bg-gray-50 p-3 rounded-md">
                                        <p className="text-gray-900 whitespace-pre-wrap">{selectedResult.prompt || '-'}</p>
                                    </div>
                                </div>
                                <div className="md:col-span-2">
                                    <label className="block text-sm font-medium text-gray-700 mb-1">执行结果</label>
                                    <div className="bg-gray-50 p-3 rounded-md max-h-60 overflow-y-auto">
                                        <pre className="text-gray-900 whitespace-pre-wrap text-sm">{selectedResult.result || '-'}</pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="flex justify-end p-6 border-t border-gray-200">
                            <button
                                onClick={handleCloseDetail}
                                className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600"
                            >
                                关闭
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}
