package com.djcps.ai.web.tools;

import cn.hutool.json.JSONUtil;
import com.djcps.ai.aibackend.tools.ToolCallService;
import com.djcps.ai.aibackend.tools.vo.RunTool;
import com.djcps.ai.aibackend.tools.vo.TestTemplate;
import com.djcps.ai.aibackend.tools.vo.ToolInfo;
import com.fasterxml.jackson.core.JsonProcessingException;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter;
import reactor.core.publisher.Flux;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.djcps.ai.aibackend.tools.AiConstant.objectMapper;


@RestController
@RequestMapping("/tools")
@Slf4j
@RequiredArgsConstructor
public class ToolsController {

    private final List<ToolCallbackProvider> toolCallbackProviders;
    public static Map<String, ToolCallback> toolCallbackMap = new HashMap<>();
    private final Map<String, ToolCallService> toolCallServiceMap ;
    /**
     * 初始化工具回调映射，将所有工具按名称缓存到Map中以提高查找性能
     */
    @PostConstruct
    public void initToolCallbackMap() {
        log.info("Initializing tool callback map...");
        for (ToolCallbackProvider provider : toolCallbackProviders) {
            for (ToolCallback callback : provider.getToolCallbacks()) {
                String toolName = callback.getToolDefinition().name();
                toolCallbackMap.put(toolName, callback);
                log.debug("Registered tool: {}", toolName);
            }
        }
        log.info("Tool callback map initialized with {} tools", toolCallbackMap.size());
    }

    @PostMapping("/list")
    public List<ToolInfo> listTools() {
        // 使用缓存的Map直接获取所有工具信息，避免重复遍历
        return toolCallbackMap.values().stream()
                .map(toolCallback -> ToolInfo.fromToolDefinition(toolCallback.getToolDefinition()))
                .toList();
    }

    @PostMapping("/callTool")
    public String callTool(@RequestBody RunTool runTool) {
        // 使用Map直接查找，时间复杂度从O(n*m)优化到O(1)
        ToolCallback toolCallback = toolCallbackMap.get(runTool.getToolName());

        if (toolCallback == null) {
            log.warn("Tool not found: {}", runTool.getToolName());
            return "工具不存在或未找到对应的回调方法";
        }

        try {
            log.debug("Calling tool: {} with parameters: {}", runTool.getToolName(), runTool.getParameters());
            String call = toolCallback.call(JSONUtil.toJsonStr(runTool.getParameters()));

            // 尝试格式化JSON响应
            try {
                Object o = objectMapper.readValue(call, Object.class);
                if (JSONUtil.isTypeJSON(o.toString())) {
                    return JSONUtil.toJsonPrettyStr(o);
                }
            } catch (JsonProcessingException e) {
                log.debug("Response is not valid JSON, returning as plain text: {}", e.getMessage());
            }

            return call;
        } catch (Exception e) {
            log.error("Error calling tool {}: {}", runTool.getToolName(), e.getMessage(), e);
            throw new RuntimeException("调用工具失败: " + e.getMessage(), e);
        }
    }


    @PostMapping("/test")
    public Flux<String> testTemplate(@RequestBody TestTemplate testTemplate) {
        ResponseBodyEmitter emitter = new ResponseBodyEmitter();
        try {
            log.info("Received test template request - Tool: {}, Business Prompt: {}, Result Type: {}",
                    testTemplate.getToolName(), testTemplate.getBusinessPrompt(), testTemplate.getResultType());
            ToolCallService toolCallService = toolCallServiceMap.get(testTemplate.getResultType());
            return toolCallService.stream(testTemplate);
        } catch (Exception e) {
            log.error("Error testing template: {}", e.getMessage(), e);
            emitter.completeWithError(e);
            return Flux.error(e);
        }
    }

}
