package com.djcps.ai.web.tools;

import cn.hutool.core.util.StrUtil;
import com.djcps.ai.common.result.WebResultExt;
import com.djcps.ai.dao.entity.UserTask;
import com.djcps.ai.service.JobService;
import com.djcps.ai.service.UserTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/task")
@Slf4j
@RequiredArgsConstructor
public class TaskController {

    private final UserTaskService userTaskService;
    private final JobService jobService;

    @PostMapping("/run/{taskId}")
    public void run(@PathVariable Long taskId) {
        userTaskService.runTask(taskId);
    }

    @PostMapping("/list")
    public WebResultExt<List<UserTask>> listTemplates() {
        List<UserTask> list = userTaskService.list();
        return new WebResultExt<>(list);
    }

    @PostMapping("/save")
    public WebResultExt<UserTask> saveTemplate(@RequestBody UserTask template) {
        try {
            // 设置用户ID
            template.setUserId("958");

            //id为空则为新增
            boolean create = template.getId() == null;
            // 保存或更新模板
            boolean success = userTaskService.saveOrUpdate(template);

            if (create) {
                String jobId = jobService.createCronTask(template.getCornExp(), template.getId());
                template.setJobId(Integer.parseInt(jobId));
                success = userTaskService.saveOrUpdate(template);
            } else {
                Long id = template.getId();
                UserTask byId = userTaskService.getById(id);
                if (byId != null) {
                    Integer jobId = byId.getJobId();
                    if (jobId != null && jobId > 0 && !StrUtil.equals(template.getCornExp(), byId.getCornExp())) {
                        jobService.updateCronTask(jobId, template.getCornExp());
                    }
                }
            }

            if (success) {
                return new WebResultExt<>(template);
            } else {
                return WebResultExt.failure("保存模板失败");
            }
        } catch (Exception e) {
            log.error("保存模板失败", e);
            return WebResultExt.failure("保存模板失败: " + e.getMessage());
        }
    }

    @PostMapping("/delete/{id}")
    public WebResultExt<Boolean> deleteTemplate(@PathVariable Long id) {
        try {
            UserTask byId = userTaskService.getById(id);
            boolean success = false;
            if (byId != null) {
                Integer jobId = byId.getJobId();
                success = userTaskService.removeById(id);
                if (success && jobId != null && jobId > 0) {
                    jobService.delJob(jobId);
                }
            }
            return new WebResultExt<>(success);
        } catch (Exception e) {
            log.error("删除模板失败", e);
            return WebResultExt.failure("删除模板失败: " + e.getMessage());
        }
    }
}
