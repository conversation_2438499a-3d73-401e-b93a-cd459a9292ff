package com.djcps.ai.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.djcps.ai.dao.base.BaseEntity;
import lombok.Data;

@Data
@TableName("user_task")
public class UserTask extends BaseEntity {
    private String userId;
    private String title;
    private String remark;
    private Long templateId;
    private String cornExp;
    private String cycleType;
    private String paramList;
    private Integer jobId;
}
